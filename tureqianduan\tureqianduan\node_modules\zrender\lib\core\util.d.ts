import { Dictionary, ArrayLike, KeyOfDistributive } from './types';
import { GradientObject } from '../graphic/Gradient';
import { ImagePatternObject } from '../graphic/Pattern';
declare const nativeSlice: (start?: number, end?: number) => any[];
export declare function guid(): number;
export declare function logError(...args: any[]): void;
export declare function clone<T extends any>(source: T): T;
export declare function merge<T extends Dictionary<any>, S extends Dictionary<any>>(target: T, source: S, overwrite?: boolean): T & S;
export declare function merge<T extends any, S extends any>(target: T, source: S, overwrite?: boolean): T | S;
export declare function mergeAll(targetAndSources: any[], overwrite?: boolean): any;
export declare function extend<T extends Dictionary<any>, S extends Dictionary<any>>(target: T, source: S): T & S;
export declare function defaults<T extends Dictionary<any>, S extends Dictionary<any>>(target: T, source: S, overlay?: boolean): T & S;
export declare const createCanvas: () => HTMLCanvasElement;
export declare function indexOf<T>(array: T[] | readonly T[] | ArrayLike<T>, value: T): number;
export declare function inherits(clazz: Function, baseClazz: Function): void;
export declare function mixin<T, S>(target: T | Function, source: S | Function, override?: boolean): void;
export declare function isArrayLike(data: any): data is ArrayLike<any>;
export declare function each<I extends Dictionary<any> | any[] | readonly any[] | ArrayLike<any>, Context>(arr: I, cb: (this: Context, value: I extends (infer T)[] | readonly (infer T)[] | ArrayLike<infer T> ? T : I extends Dictionary<any> ? I extends Record<infer K, infer T> ? T : unknown : unknown, index?: I extends any[] | readonly any[] | ArrayLike<any> ? number : keyof I & string, arr?: I) => void, context?: Context): void;
export declare function map<T, R, Context>(arr: readonly T[], cb: (this: Context, val: T, index?: number, arr?: readonly T[]) => R, context?: Context): R[];
export declare function reduce<T, S, Context>(arr: readonly T[], cb: (this: Context, previousValue: S, currentValue: T, currentIndex?: number, arr?: readonly T[]) => S, memo?: S, context?: Context): S;
export declare function filter<T, Context>(arr: readonly T[], cb: (this: Context, value: T, index: number, arr: readonly T[]) => boolean, context?: Context): T[];
export declare function find<T, Context>(arr: readonly T[], cb: (this: Context, value: T, index?: number, arr?: readonly T[]) => boolean, context?: Context): T;
export declare function keys<T extends object>(obj: T): (KeyOfDistributive<T> & string)[];
export declare type Bind1<F, Ctx> = F extends (this: Ctx, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Bind2<F, Ctx, T1> = F extends (this: Ctx, a: T1, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Bind3<F, Ctx, T1, T2> = F extends (this: Ctx, a: T1, b: T2, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Bind4<F, Ctx, T1, T2, T3> = F extends (this: Ctx, a: T1, b: T2, c: T3, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Bind5<F, Ctx, T1, T2, T3, T4> = F extends (this: Ctx, a: T1, b: T2, c: T3, d: T4, ...args: infer A) => infer R ? (...args: A) => R : unknown;
declare type BindFunc<Ctx> = (this: Ctx, ...arg: any[]) => any;
interface FunctionBind {
    <F extends BindFunc<Ctx>, Ctx>(func: F, ctx: Ctx): Bind1<F, Ctx>;
    <F extends BindFunc<Ctx>, Ctx, T1 extends Parameters<F>[0]>(func: F, ctx: Ctx, a: T1): Bind2<F, Ctx, T1>;
    <F extends BindFunc<Ctx>, Ctx, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1]>(func: F, ctx: Ctx, a: T1, b: T2): Bind3<F, Ctx, T1, T2>;
    <F extends BindFunc<Ctx>, Ctx, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1], T3 extends Parameters<F>[2]>(func: F, ctx: Ctx, a: T1, b: T2, c: T3): Bind4<F, Ctx, T1, T2, T3>;
    <F extends BindFunc<Ctx>, Ctx, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1], T3 extends Parameters<F>[2], T4 extends Parameters<F>[3]>(func: F, ctx: Ctx, a: T1, b: T2, c: T3, d: T4): Bind5<F, Ctx, T1, T2, T3, T4>;
}
export declare const bind: FunctionBind;
export declare type Curry1<F, T1> = F extends (a: T1, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Curry2<F, T1, T2> = F extends (a: T1, b: T2, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Curry3<F, T1, T2, T3> = F extends (a: T1, b: T2, c: T3, ...args: infer A) => infer R ? (...args: A) => R : unknown;
export declare type Curry4<F, T1, T2, T3, T4> = F extends (a: T1, b: T2, c: T3, d: T4, ...args: infer A) => infer R ? (...args: A) => R : unknown;
declare type CurryFunc = (...arg: any[]) => any;
declare function curry<F extends CurryFunc, T1 extends Parameters<F>[0]>(func: F, a: T1): Curry1<F, T1>;
declare function curry<F extends CurryFunc, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1]>(func: F, a: T1, b: T2): Curry2<F, T1, T2>;
declare function curry<F extends CurryFunc, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1], T3 extends Parameters<F>[2]>(func: F, a: T1, b: T2, c: T3): Curry3<F, T1, T2, T3>;
declare function curry<F extends CurryFunc, T1 extends Parameters<F>[0], T2 extends Parameters<F>[1], T3 extends Parameters<F>[2], T4 extends Parameters<F>[3]>(func: F, a: T1, b: T2, c: T3, d: T4): Curry4<F, T1, T2, T3, T4>;
export { curry };
export declare function isArray(value: any): value is any[];
export declare function isFunction(value: any): value is Function;
export declare function isString(value: any): value is string;
export declare function isStringSafe(value: any): value is string;
export declare function isNumber(value: any): value is number;
export declare function isObject<T = unknown>(value: T): value is (object & T);
export declare function isBuiltInObject(value: any): boolean;
export declare function isTypedArray(value: any): boolean;
export declare function isDom(value: any): value is HTMLElement;
export declare function isGradientObject(value: any): value is GradientObject;
export declare function isImagePatternObject(value: any): value is ImagePatternObject;
export declare function isRegExp(value: unknown): value is RegExp;
export declare function eqNaN(value: any): boolean;
export declare function retrieve<T>(...args: T[]): T;
export declare function retrieve2<T, R>(value0: T, value1: R): T | R;
export declare function retrieve3<T, R, W>(value0: T, value1: R, value2: W): T | R | W;
declare type SliceParams = Parameters<typeof nativeSlice>;
export declare function slice<T>(arr: ArrayLike<T>, ...args: SliceParams): T[];
export declare function normalizeCssArray(val: number | number[]): number[];
export declare function assert(condition: any, message?: string): void;
export declare function trim(str: string): string;
export declare function setAsPrimitive(obj: any): void;
export declare function isPrimitive(obj: any): boolean;
interface MapInterface<T, KEY extends string | number = string | number> {
    delete(key: KEY): boolean;
    has(key: KEY): boolean;
    get(key: KEY): T | undefined;
    set(key: KEY, value: T): this;
    keys(): KEY[];
    forEach(callback: (value: T, key: KEY) => void): void;
}
export declare class HashMap<T, KEY extends string | number = string | number> {
    data: MapInterface<T, KEY>;
    constructor(obj?: HashMap<T, KEY> | {
        [key in KEY]?: T;
    } | KEY[]);
    hasKey(key: KEY): boolean;
    get(key: KEY): T;
    set(key: KEY, value: T): T;
    each<Context>(cb: (this: Context, value?: T, key?: KEY) => void, context?: Context): void;
    keys(): KEY[];
    removeKey(key: KEY): void;
}
export declare function createHashMap<T, KEY extends string | number = string | number>(obj?: HashMap<T, KEY> | {
    [key in KEY]?: T;
} | KEY[]): HashMap<T, KEY>;
export declare function concatArray<T, R>(a: ArrayLike<T>, b: ArrayLike<R>): ArrayLike<T | R>;
export declare function createObject<T>(proto?: object, properties?: T): T;
export declare function disableUserSelect(dom: HTMLElement): void;
export declare function hasOwn(own: object, prop: string): boolean;
export declare function noop(): void;
export declare const RADIAN_TO_DEGREE: number;
