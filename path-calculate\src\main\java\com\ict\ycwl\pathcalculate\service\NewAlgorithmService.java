package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult;
import com.ict.ycwl.pathcalculate.config.AlgorithmConfig;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.service.adapter.DatabaseToAlgorithmAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新算法服务类
 * 提供新算法的完整执行流程，可以被CalculateServiceImpl调用
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-11
 */
@Slf4j
@Service
public class NewAlgorithmService {

    @Autowired
    private DatabaseToAlgorithmAdapter databaseToAlgorithmAdapter;

    @Autowired
    private PathPlanningUtils pathPlanningUtils;

    @Autowired
    private AlgorithmConfig algorithmConfig;

    /**
     * 使用新算法执行路径规划
     * 
     * @param apiKey API密钥
     * @return 路径规划结果
     * @throws Exception 执行异常
     */
    public List<ResultRoute> executeNewAlgorithm(String apiKey) throws Exception {
        log.info("开始执行新算法路径规划");
        
        // 验证配置
        if (!algorithmConfig.isValid()) {
            throw new IllegalArgumentException("算法配置参数无效");
        }
        
        try {
            // 1. 从数据库加载数据并转换为算法格式
            log.info("步骤1: 加载数据");
            PathPlanningRequest request = databaseToAlgorithmAdapter.loadDataFromDatabase();
            log.info("数据加载完成，聚集区: {}, 中转站: {}, 班组: {}", 
                    request.getAccumulations().size(), 
                    request.getTransitDepots().size(), 
                    request.getTeams().size());
            
            // 验证输入数据
            validateInputData(request);
            
            // 2. 执行算法
            log.info("步骤2: 执行算法");
            long startTime = System.currentTimeMillis();
            PathPlanningResult algorithmResult = pathPlanningUtils.calculateWithSpring(request);
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 3. 检查算法执行结果
            if (!algorithmResult.isSuccess()) {
                log.error("新算法执行失败: {}", algorithmResult.getErrorMessage());
                throw new RuntimeException("路径规划算法执行失败: " + algorithmResult.getErrorMessage());
            }
            
            log.info("新算法执行成功，生成路线数: {}, 执行时间: {}ms", 
                    algorithmResult.getRoutes().size(), executionTime);
            
            // 4. 转换结果格式
            log.info("步骤3: 转换结果格式");
            List<ResultRoute> resultRoutes = databaseToAlgorithmAdapter.convertAlgorithmResult(algorithmResult, apiKey);
            log.info("结果转换完成，最终路线数: {}", resultRoutes.size());
            
            // 5. 验证输出结果
            validateOutputData(resultRoutes);
            
            return resultRoutes;
            
        } catch (Exception e) {
            log.error("新算法执行过程中发生异常", e);
            throw e; // 重新抛出异常，让调用者处理
        }
    }

    /**
     * 验证输入数据的有效性
     */
    private void validateInputData(PathPlanningRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求对象不能为空");
        }
        
        if (request.getAccumulations() == null || request.getAccumulations().isEmpty()) {
            throw new IllegalArgumentException("聚集区数据不能为空");
        }
        
        if (request.getTransitDepots() == null || request.getTransitDepots().isEmpty()) {
            throw new IllegalArgumentException("中转站数据不能为空");
        }
        
        if (request.getTeams() == null || request.getTeams().isEmpty()) {
            throw new IllegalArgumentException("班组数据不能为空");
        }
        
        // 检查最小数据量要求
        if (request.getAccumulations().size() < algorithmConfig.getParams().getMinAccumulationCount()) {
            throw new IllegalArgumentException("聚集区数量不足，至少需要 " + 
                    algorithmConfig.getParams().getMinAccumulationCount() + " 个");
        }
        
        log.debug("输入数据验证通过");
    }

    /**
     * 验证输出数据的有效性
     */
    private void validateOutputData(List<ResultRoute> resultRoutes) {
        if (resultRoutes == null) {
            throw new RuntimeException("算法结果转换失败，结果为空");
        }
        
        // 检查路线数量限制
        if (resultRoutes.size() > algorithmConfig.getParams().getMaxRouteCount()) {
            log.warn("生成的路线数量 {} 超过了配置的最大值 {}", 
                    resultRoutes.size(), algorithmConfig.getParams().getMaxRouteCount());
        }
        
        // 验证每条路线的基本字段
        for (int i = 0; i < resultRoutes.size(); i++) {
            ResultRoute route = resultRoutes.get(i);
            if (route.getRouteName() == null || route.getRouteName().trim().isEmpty()) {
                throw new RuntimeException("第 " + (i + 1) + " 条路线的名称为空");
            }
            if (route.getTransitDepotId() == null) {
                throw new RuntimeException("第 " + (i + 1) + " 条路线的中转站ID为空");
            }
        }
        
        log.debug("输出数据验证通过");
    }

    /**
     * 检查新算法是否可用
     */
    public boolean isNewAlgorithmAvailable() {
        try {
            return algorithmConfig != null && 
                   algorithmConfig.isValid() && 
                   algorithmConfig.isEnableNewAlgorithm() &&
                   databaseToAlgorithmAdapter != null &&
                   pathPlanningUtils != null;
        } catch (Exception e) {
            log.warn("检查新算法可用性时发生异常", e);
            return false;
        }
    }

    /**
     * 获取算法配置信息
     */
    public AlgorithmConfig getAlgorithmConfig() {
        return algorithmConfig;
    }

    /**
     * 执行算法健康检查
     */
    public boolean healthCheck() {
        try {
            // 检查各个组件
            if (!isNewAlgorithmAvailable()) {
                return false;
            }
            
            // 可以添加更多的健康检查逻辑
            // 比如检查数据库连接、算法组件状态等
            
            return true;
        } catch (Exception e) {
            log.error("算法健康检查失败", e);
            return false;
        }
    }
}
