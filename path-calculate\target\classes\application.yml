# ===== 原始配置（已注释保留） =====
#spring:
#  application:
#    # 应用名称
#    name: pathcalculate
#  datasource:
#    url: *******************************************************************************************************&
#    username: root
#    password: 123
#    driver-class-name: com.mysql.jdbc.Driver

# ===== 临时本地数据库配置（确保另存为新版本功能正常工作） =====
# 注意：这是临时配置，用于解决数据库连接问题
# 一旦Nacos配置问题解决，可以注释掉此部分，恢复使用Nacos配置
spring:
  application:
    name: pathcalculate
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: *************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      initial-size: 15
      min-idle: 15
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: ""
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      connection-properties: false

# ===== 自定义配置 =====
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt

# 算法配置
algorithm:
  clustering:
    post-optimization:
      enabled: true

# 日志配置 - 屏蔽第三方库的DEBUG日志
logging:
  level:
    # 第三方库日志控制
    com.graphhopper.jsprit: WARN
    org.optaplanner: WARN
    org.drools: WARN
    # 算法详细调试日志
    com.ict.ycwl.pathcalculate.algorithm: INFO
