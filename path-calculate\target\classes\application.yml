# 临时本地配置 - 覆盖Nacos配置以确保正确的数据库连接
spring:
  application:
    # 应用名称
    name: pathcalculate
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      master:
        url: *************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: ********************************************************************************************************************************
        username: root
        password: aA13717028793#
        driver-class-name: com.mysql.jdbc.Driver
      initial-size: 15
      min-idle: 15
      max-active: 200
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: ""
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: false
      connection-properties: false

# 自定义配置
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt

# 算法配置
algorithm:
  clustering:
    post-optimization:
      enabled: true

# 日志配置 - 屏蔽第三方库的DEBUG日志
logging:
  level:
    # 第三方库日志控制
    com.graphhopper.jsprit: WARN
    org.optaplanner: WARN
    org.drools: WARN
    # 算法详细调试日志
    com.ict.ycwl.pathcalculate.algorithm: INFO
