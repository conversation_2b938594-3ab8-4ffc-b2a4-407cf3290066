{"name": "xml-name-validator", "description": "Validates whether a string matches the production for an XML name or qualified name", "keywords": ["xml", "name", "qname"], "version": "4.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "Apache-2.0", "repository": "jsdom/xml-name-validator", "main": "lib/xml-name-validator.js", "files": ["lib/"], "scripts": {"test": "mocha", "lint": "eslint ."}, "devDependencies": {"@domenic/eslint-config": "^1.4.0", "benchmark": "^2.1.4", "eslint": "^7.32.0", "mocha": "^9.1.1"}, "engines": {"node": ">=12"}}