package com.ict.ycwl.pathcalculate.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;


public class MySQLConnection {


    //4G
   // private static final String URL = "******************************************************************************************************"; // 数据库地址
//    //烟草
//    //private static final String URL = "****************************************************************************************************"; // 数据库地址
//    //540
    private static final String URL = "********************************************************************"; // 数据库地址
    private static final String USER = "root"; // 数据库用户名
//    //540
    private static final String PASSWORD = "16281628"; // 数据库密码
   //private static final String PASSWORD = "123"; // 数据库密码
//    //private static final String PASSWORD = "xlghsj@8963900"; // 数据库密码

    public static Connection getConnection() {
        Connection connection = null;
        try {

            // 加载 JDBC 驱动
            Class.forName("com.mysql.jdbc.Driver");
            // 获取数据库连接
            connection = DriverManager.getConnection(URL, USER, PASSWORD);
            System.out.println("数据库连接成功！");
        } catch (ClassNotFoundException e) {
            System.out.println("找不到 JDBC 驱动！" + e.getMessage());
        } catch (SQLException e) {
            System.out.println("数据库连接失败！" + e.getMessage());
        }
        return connection;
    }
}
